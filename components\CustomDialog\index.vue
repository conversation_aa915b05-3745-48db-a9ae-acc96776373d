<template>
  <uni-popup
    ref="popup"
    :show="modelValue"
    type="center"
    background-color="transparent"
    @change="handleChange"
    :is-mask-click="isMaskClick"
    :style="{ zIndex: zIndex }"
  >
    <view class="custom-dialog">
      <!-- 标题区域 -->
      <view class="dialog-header" v-if="title">
        <text class="dialog-title">{{ title }}</text>
      </view>
      
      <!-- 内容区域 -->
      <scroll-view
        class="dialog-content"
        :style="fixedHeight ? { height: contentHeight } : { minHeight: contentHeight }"
        scroll-y
      >
        <component
          :is="content"
          v-if="content"
          v-bind="contentProps"
          ref="contentRef"
        />
        <slot v-else></slot>
      </scroll-view>
      
      <!-- 按钮区域 -->
      <view class="dialog-footer" v-if="showConfirm || showCancel">
        <button 
          class="dialog-btn cancel" 
          v-if="showCancel"
          :style="cancelStyle"
          :throttleTime="500"
          @click="handleCancel"
        >{{ cancelText }}</button>
        <button 
          class="dialog-btn confirm" 
          v-if="showConfirm"
          :style="confirmStyle"
          :loading="loading"
          :disabled="loading"
          :throttleTime="500"
          @click="handleConfirm"
        >{{ confirmText }}</button>
      </view>
    </view>
  </uni-popup>
</template>

<script>
import PageContainer from '@/components/PageContainer/index.vue'
export default {
  name: 'CustomDialog'
}
</script>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue'
import { $t } from '@/locale/index.js'

const props = defineProps({
  // 控制弹窗显示
  modelValue: {
    type: Boolean,
    default: false
  },
  // 标题
  title: {
    type: String,
    default: ''
  },
  // 内容组件
  content: {
    type: [Object, Function],
    default: null
  },
  // 传递给内容组件的props
  contentProps: {
    type: Object,
    default: () => ({})
  },
  // 是否显示确认按钮
  showConfirm: {
    type: Boolean,
    default: true
  },
  // 是否显示取消按钮
  showCancel: {
    type: Boolean,
    default: true
  },
  // 确认按钮文字
  confirmText: {
    type: String,
    default: $t('common.confirm')
  },
  // 取消按钮文字
  cancelText: {
    type: String,
    default: $t('common.cancel')
  },
  // 点击遮罩是否关闭
  closeOnClickMask: {
    type: Boolean,
    default: false
  },
  // 内容区域高度
  contentHeight: {
    type: String,
    default: '40vh'
  },
  // 是否固定高度
  fixedHeight: {
    type: Boolean,
    default: false
  },
  // 是否可以点击关闭遮罩
  isMaskClick: {
    type: Boolean,
    default: true
  },
  // 控制弹窗的层级
  zIndex: {
    type: Number,
    default: 1000
  },
  // 确认按钮样式
  confirmStyle: {
    type: Object,
    default: () => ({})
  },
  // 取消按钮样式
  cancelStyle: {
    type: Object,
    default: () => ({})
  },
  // 是否加载中
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'cancel', 'confirm'])
const popup = ref(null)
const contentRef = ref(null)

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    popup.value?.open()
  } else {
    popup.value?.close()
  }
})

// 处理弹窗状态变化
const handleChange = (e) => {
  if (!e.show && props.modelValue) {
    emit('update:modelValue', false)
  }
}

// 关闭弹窗方法
const close = () => {
  popup.value?.close()
  emit('update:modelValue', false)
}

// 处理取消
const handleCancel = async () => {
  if (contentRef.value?.onCancel) {
    await contentRef.value.onCancel(close)
  } else {
    emit('cancel', close)
    close()
  }
}

// 处理确认
const handleConfirm = async () => {
  if (contentRef.value?.onConfirm) {
    await contentRef.value.onConfirm(close)
  } else {
    emit('confirm', close)
  }
}

// 暴露方法给父组件
defineExpose({
  close
})
</script>

<style lang="scss" scoped>
.custom-dialog {
  position: relative;
  width: 80vw;
  max-width: 600rpx;
  background-color: var(--bg-color, #fff);
  border-radius: 16rpx;
  overflow: hidden;
  
  .dialog-header {
    padding: 30rpx;
    text-align: center;
    border-bottom: 1px solid var(--border-color, #eee);
    
    .dialog-title {
      font-size: 32rpx;
      font-weight: 500;
      color: var(--text-color-primary, #333);
    }
  }
  
  .dialog-content {
    padding: 0 30rpx;
    box-sizing: border-box;
    overflow: hidden;
  }
  
  .dialog-footer {
    display: flex;
    padding: 20rpx;
    border-top: 1px solid var(--border-color, #eee);
    
    .dialog-btn {
      flex: 1;
      margin: 0 10rpx;
      min-height: 80rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 8rpx;
      font-size: 28rpx;
      line-height: 1.2;
      
      &.cancel {
        background-color: var(--bg-color-secondary, #f5f5f5);
        color: var(--text-color-secondary, #666);
      }
      
      &.confirm {
        background-color: var(--primary-color);
        color: #fff;
      }
    }
  }
}

// 添加以下全局样式覆盖
:deep(.uni-popup__wrapper) {
  /* #ifndef APP-NVUE */
  display: block;
  /* #endif */
}

:deep(.uni-popup__wrapper-box) {
  /* #ifndef APP-NVUE */
  display: block;
  /* #endif */
  padding: 0;
}
</style> 